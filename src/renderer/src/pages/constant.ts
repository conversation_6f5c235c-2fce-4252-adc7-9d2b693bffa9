import { AccountState } from '@renderer/components/AccountState/AccountState'
import { AccountStateLogin } from '@renderer/components/AccountState/AccountStateLogin'
import {
  AssetLibraryPreview,
  AssetLibraryUpload,
  AssetLibraryGroupDialog,
  AssetLibrary,
  AssetLibraryDownload,
} from '@renderer/components/AssetLibrary'
import { AddSitGroup } from './Space/components/AddSitGroup'
import { CollSelectSpace } from './Space/components/CollSelectSpace'
import { ExportTasks } from '@renderer/components/ExportTasks'
import { SuperDirector } from './Publish/video/beforeDialog/SuperDirector'
import { MarketAgent } from './Publish/video/beforeDialog/MarketAgent'

/**
 * dialog 配置
 */
export const dialogPropsMap = <const>{
  assetLibraryUpload: {
    Element: AssetLibraryUpload,
  },
  assetLibraryPreview: {
    Element: AssetLibraryPreview,
  },
  AssetLibraryGroupDialog: {
    Element: AssetLibraryGroupDialog,
  },
  assetLibraryDownload: {
    Element: AssetLibraryDownload,
  },
  superDirector: {
    Element: SuperDirector,
  },
  marketAgent: {
    Element: MarketAgent,
  },
  exportTasks: {
    Element: ExportTasks,
  },
  assetLibrary: {
    Element: AssetLibrary,
    dialogContentProps: {
      className: 'w-[90vw] h-[90vh] max-w-[90vw] max-h-[90vh] p-0 flex flex-col',
    },
  },
  accountState: {
    Element: AccountState,
    dialogContentProps: {
      className: 'max-w-[90vw] w-[auto] max-h-[90vh] p-0 bg-[#ffffff]',
    },
  },
  accountStateLogin: {
    Element: AccountStateLogin,
    dialogContentProps: {
      className: 'max-w-[90vw] max-h-[90vh] p-0 bg-[#ffffff]',
    },
  },

  addSitGroup: {
    Element: AddSitGroup,
  },
  collSelectSpace: {
    Element: CollSelectSpace,
    dialogContentProps: {
      className: 'max-w-[654px] max-h-[90vh] flex flex-col p-0',
    },
  },
}
