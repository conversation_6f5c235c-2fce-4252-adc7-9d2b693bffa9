import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@renderer/shadcn-components/dropdown-menu'
import UploadIcon from '@renderer/assets/publish/upload.svg?react'
import { electronService } from '@renderer/infrastructure/services'
import { useAssetLibrary } from '@renderer/hooks/useAssetLibrary'
import type { IAssetLibraryItem, VideoFileInfo } from '@renderer/infrastructure/model'
import { noop, useSystem } from '@renderer/pages/context'
import { useLocalFileService } from '@renderer/infrastructure/services/application-service/infrastructure-service/LocalFileService'
import { useContextStore } from '@renderer/store/contextStore'
import { useMemo } from 'react'

// 使用函数重载
type AddVideoProps<T extends 'single' | 'multiple'> = {
  mode?: T
  onChange?: T extends 'single'
    ? (videoInfo: VideoFileInfo) => void
    : (videoInfo: VideoFileInfo[]) => void
  selectSuperIds?: string[]
  selectMediaIds?: string[]
}

export function AddVideo<T extends 'single'>(props: AddVideoProps<T>): JSX.Element
export function AddVideo<T extends 'multiple'>(props: AddVideoProps<T>): JSX.Element
export function AddVideo<T extends 'single' | 'multiple' = 'multiple'>({
  onChange,
  mode = 'multiple' as T,
  selectSuperIds,
  selectMediaIds,
}: AddVideoProps<T>) {
  const currentTeam = useContextStore((state) => state.currentTeam)
  const { selectMultipleVideoAsset, selectSingleVideoAsset } = useAssetLibrary()
  const { onSetDialog, onSetDialogSub } = useSystem()
  const localFileService = useLocalFileService()

  async function selectFromLocalFile() {
    if (mode === 'single') {
      const videoInfo = await electronService.openVideoFile()
      if (videoInfo === null) return
      // @ts-ignore - 类型推断限制，但实际运行时是正确的
      onChange?.(videoInfo)
    } else {
      const videoInfo = await electronService.openVideoFiles()
      if (videoInfo === null) return
      // @ts-ignore - 类型推断限制，但实际运行时是正确的
      onChange?.(videoInfo)
    }
  }

  async function selectFromAssetLibrary() {
    if (mode === 'single') {
      const videoInfo = await selectSingleVideoAsset()
      if (videoInfo === null) return
      // @ts-ignore - 类型推断限制，但实际运行时是正确的
      onChange?.(videoInfo)
    } else {
      const videoInfo = await selectMultipleVideoAsset()
      if (videoInfo === null) return
      // @ts-ignore - 类型推断限制，但实际运行时是正确的
      onChange?.(videoInfo)
    }
  }

  async function selectSuperFile() {
    onSetDialog((dialogMap) => ({
      ...dialogMap.superDirector,
      dialogContentProps: {
        className: 'super-dialog w-auto bg-white p-0',
        style: { maxWidth: 'none' },
      },
      elementProps: {
        selectSuperIds,
        onChangeFile: (files: IAssetLibraryItem[]) => {
          onSetDialogSub((dialogMap) => ({
            ...dialogMap.assetLibraryDownload,
            elementProps: {
              list: files,
              async onDone(list) {
                const res = await Promise.all(
                  list.map(async (item) => {
                    const value = await localFileService.getVideoFileInfo(item.filePath)
                    value.superId = item.id
                    return value
                  }),
                )
                // @ts-ignore - 类型推断限制，但实际运行时是正确的
                onChange?.(res)
                onSetDialog(noop)
              },
            },
            dialogContentProps: {
              onInteractOutside(event) {
                event.preventDefault()
              },
            },
          }))
        },
      },
    }))
  }

  async function selectMarketAgentFile() {
    console.log('selectMediaIds', selectMediaIds)
    onSetDialog((dialogMap) => ({
      ...dialogMap.marketAgent,
      dialogContentProps: {
        className: 'super-dialog w-auto bg-white p-0',
        style: { maxWidth: 'none' },
      },
      elementProps: {
        selectMarketAgentIds: selectMediaIds,
        onChangeFile: (files: IAssetLibraryItem[]) => {
          onSetDialogSub((dialogMap) => ({
            ...dialogMap.assetLibraryDownload,
            elementProps: {
              list: files,
              async onDone(list) {
                console.log('onDone', list)
                const res = await Promise.all(
                  list.map(async (item) => {
                    const value = await localFileService.getVideoFileInfo(item.filePath)
                    value.mediaId = item.id
                    return value
                  }),
                )
                // @ts-ignore - 类型推断限制，但实际运行时是正确的
                onChange?.(res)
                onSetDialog(noop)
              },
            },
            dialogContentProps: {
              onInteractOutside(event) {
                event.preventDefault()
              },
            },
          }))
        },
      },
    }))
  }

  const superdirEnabled = useMemo(() => {
    const superdir = currentTeam?.components?.find((c) => c.name === 'superdir')
    return !!superdir?.enabled
  }, [currentTeam])

  const marketAgentEnabled = useMemo(() => {
    const marketAgent = currentTeam?.components?.find((c) => c.name === 'marketAgent')
    return !!marketAgent?.enabled
  }, [currentTeam])

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        className="flex h-full w-full cursor-pointer flex-col items-center justify-center rounded-md bg-secondary"
        onClick={(e) => {
          e.stopPropagation()
        }}
      >
        <div className="mb-2">
          <UploadIcon className={'h-4 w-4'} />
        </div>
        <div className="text-sm text-gray-800">上传</div>
      </DropdownMenuTrigger>
      <DropdownMenuContent side="right" align="start">
        <DropdownMenuItem onClick={selectFromAssetLibrary}>素材库</DropdownMenuItem>
        <DropdownMenuItem onClick={selectFromLocalFile}>本地选择</DropdownMenuItem>
        {superdirEnabled && <DropdownMenuItem onClick={selectSuperFile}>超级编导</DropdownMenuItem>}
        {marketAgentEnabled && (
          <DropdownMenuItem onClick={selectMarketAgentFile}>AI营销智能体</DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
