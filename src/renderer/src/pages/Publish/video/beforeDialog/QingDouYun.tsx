import { features, type IAssetLibraryItem } from '@renderer/infrastructure/model'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { DialogContent } from '@renderer/shadcn-components/dialog'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  DialogClose,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@renderer/shadcn-components/ui/dialog'
import { useInnerContextStore } from '@renderer/store/contextStore'
import { useEffect, useMemo, useRef } from 'react'
import { usePublishVideoDialogContext } from './usePublishVideoDialog'

const qdyIdMaps = {}

export function QingDouYun({
  selectQdyIds,
  onClose,
  onChangeFile,
}: {
  selectQdyIds: string[]
  onClose: React.Dispatch<React.SetStateAction<boolean>>
  onChangeFile: (file: IAssetLibraryItem[]) => void
}) {
  const { close } = usePublishVideoDialogContext()
  const { openFeature } = useFeatureManager()
  const currentTeam = useInnerContextStore((state) => state.currentTeam)

  const qdyUrl = useMemo(() => {
    const aToken = currentTeam?.components?.find((c) => c.name === 'qingdouyun')?.componentArgs?.token
    if (aToken) {
      const params = new URLSearchParams()
      params.set('token', aToken as string)
      return `${import.meta.env.VITE_QINGDOUYUN_URL}/#/video_select_drawer?${params.toString()}`
    }
    return null
  }, [currentTeam])

  const iframeRef = useRef<HTMLIFrameElement>(null)
  useEffect(() => {
    const handleMessage = async (event: { data: string }) => {
      try {
        const { type, ...rest } = JSON.parse(event.data)

        if (type === 'mounted') {
          if (selectQdyIds.length > 0 && iframeRef.current && iframeRef.current.contentWindow) {
            const selectData = new Set(selectQdyIds)
            iframeRef.current.contentWindow.postMessage(
              JSON.stringify({
                from: 'videoSelect',
                type: 'defaultVideos',
                data: [...selectData].map((id) => qdyIdMaps[id]).filter(Boolean),
              }),
              import.meta.env.VITE_QINGDOUYUN_URL,
            )
            console.log(
              '已发送',
              JSON.stringify({
                from: 'videoSelect',
                type: 'defaultVideos',
                data: selectQdyIds,
              }),
            )
          }

          return
        }

        if (type === 'close') {
          onClose(false)
          return
        }

        const ret: { id: string; fileName: string; filePath: string }[] = []
        rest.data.forEach((item: Record<string, string>) => {
          ret.push({
            id: item.id,
            fileName: item.name,
            filePath: item.file,
          })
          qdyIdMaps[item.id] = item
        })

        onChangeFile(ret as IAssetLibraryItem[])
      } catch {
        //
      }
    }

    // 添加事件监听器
    window.addEventListener('message', handleMessage)

    // 清理函数：组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('message', handleMessage)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onChangeFile, onClose])

  return qdyUrl ? (
    <div className="relative">
      <iframe ref={iframeRef} className="h-[90vh] w-[80vw]" src={qdyUrl} />
    </div>
  ) : (
    <DialogContent>
      <DialogHeader>
        <DialogTitle>青豆云</DialogTitle>
        <DialogDescription></DialogDescription>
      </DialogHeader>
      <p>青豆云账号 token 未设置</p>
      <DialogFooter>
        <DialogClose asChild>
          <Button type="button" variant="outline">
            取消
          </Button>
        </DialogClose>
        <Button
          type="button"
          onClick={() => {
            openFeature(features.团队管理, { tabsDefaultValue: 'plugins' })
            onClose(false)
            close()
          }}
          variant="default"
        >
          跳转设置
        </Button>
      </DialogFooter>
    </DialogContent>
  )
}
