import { Input } from '@renderer/shadcn-components/ui/input'
import { Button } from '@renderer/shadcn-components/ui/button'
import { Check, Plus, X } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import { cn } from '@renderer/lib/utils'
import { useComposition } from '@renderer/hooks/useComposition'

export function Keyword({
  list,
  onDelete,
  onSubmit,
  hasAdd = true,
  selected,
  onSelect,
  name = '话题',
  addBefore = false,
  maxLength,
}: {
  list: { name: string; id: string }[]
  onDelete?: (index: number) => void
  onSubmit: (text: string) => void
  hasAdd?: boolean
  selected: string[]
  onSelect?: (value: string[]) => void
  name?: string
  addBefore?: boolean
  maxLength?: number
}) {
  // 显示 input
  const [showInput, setShowInput] = useState(false)
  const [input, setInput] = useState<string>('')

  // 处理输入法合成事件
  const { onCompositionStart, onCompositionEnd, compositionState } = useComposition()
  const onSubmitInput = () => {
    if (input) {
      if (list.find((item) => item.name === input)) {
        toast.error(`${name}已存在`)
        return
      }
      onSubmit(input)
      setInput('')
      setShowInput(false)
    }
  }

  // 是否在maxLength以内
  const hasMaxLength = maxLength && list.length >= maxLength

  const addRender = () => {
    if (hasAdd && !hasMaxLength)
      return (
        <div>
          {!showInput ? (
            <Button type="button" size="sm" variant="outline" onClick={() => setShowInput(true)}>
              <Plus className="h-4 w-4" />
              添加{name} {maxLength ? `(${list.length}/${maxLength})` : ''}
            </Button>
          ) : (
            <div className="relative flex w-full max-w-sm items-center space-x-2">
              <Input
                autoFocus
                className="h-8 w-auto pr-8"
                placeholder={`添加${name}`}
                value={input}
                onChange={(e) => {
                  setInput(e.target.value)
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    onSubmitInput()
                  }
                }}
              />
              <Button
                onClick={(e) => {
                  e.preventDefault
                  e.stopPropagation()
                  onSubmitInput()
                }}
                className="absolute right-1 h-6 w-6"
                type="button"
                variant="ghost"
                size="icon"
              >
                <Check className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      )
    return null
  }
  return (
    <div className="flex flex-wrap items-center gap-3">
      {addBefore && addRender()}
      {list.length > 0 && (
        <>
          {list.map((item, index) => (
            <div
              onClick={() => {
                if (selected.includes(item.id)) {
                  onSelect?.(selected.filter((id) => id !== item.id))
                } else {
                  onSelect?.([...selected, item.id])
                }
              }}
              key={index}
              className={cn(
                'flex h-8 cursor-pointer items-center rounded-md border border-muted bg-secondary py-2 pl-2 pr-1 text-sm hover:bg-accent',
                { 'border-primary bg-accent': selected.includes(item.id), 'pr-2': !onDelete },
              )}
            >
              <span>{item.name}</span>
              {!!onDelete && (
                <Button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault
                    e.stopPropagation()
                    onSelect?.(selected.filter((id) => id !== item.id))
                    onDelete(index)
                  }}
                  variant="ghost"
                  size="icon"
                  className="ml-2 h-5 w-5"
                >
                  <X className="h-4 w-4 hover:bg-primary hover:text-primary-foreground" />
                </Button>
              )}
            </div>
          ))}
        </>
      )}
      {!addBefore && addRender()}
    </div>
  )
}
