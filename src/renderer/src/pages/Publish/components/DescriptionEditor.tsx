import TooltipButton from '@renderer/components/tooltipButton'
import { TopicsDialog } from './topics'
import type { Editor } from '@tiptap/react'
import { EditorProvider } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { pasteRegex, Topic } from '@renderer/pages/Publish/components/TiptapEditor/extensions/topic'
import { useEffect, useRef } from 'react'
import { Button } from '@renderer/shadcn-components/ui/button'
import TopicTutorialGif from '@renderer/assets/publish/topic-tutorial.gif'

export function DescriptionEditor({
  description,
  onChange,
  isSupportTopic = true,
  onSyncB,
  editorRef,
}: {
  description: string
  onChange: (description: string) => void
  // 是否支持话题
  isSupportTopic?: boolean
  onSyncB?: () => void
  editorRef?: React.MutableRefObject<Editor | null>
}) {
  const editerRef = useRef<Editor>()
  function insertTopic() {
    editerRef.current?.commands.focus()
    editerRef.current?.commands.insertContent('#')
  }

  const oldDescription = useRef(description)

  useEffect(() => {
    if (!oldDescription.current) {
      oldDescription.current = description
      editerRef.current?.commands.setContent(description)
    }
  }, [description])

  return (
    <div className="flex w-full flex-col gap-3">
      <EditorProvider
        extensions={[
          StarterKit.configure({
            blockquote: false,
            bulletList: false,
            codeBlock: false,
            hardBreak: {
              keepMarks: false,
              HTMLAttributes: {},
            },
            orderedList: false,
            heading: false,
            horizontalRule: false,
            listItem: false,
            bold: false,
            italic: false,
            strike: false,
            code: false,
          }),
          Placeholder.configure({ placeholder: '请输入描述...' }),
          ...(isSupportTopic ? [Topic] : []),
        ]}
        onUpdate={({ editor }) => {
          const html = editor.getHTML()
          // 标准化HTML中的换行符，确保一致性
          const normalizedHtml = html.replace(/\r\n/g, '\n').replace(/\r/g, '\n')
          onChange(normalizedHtml)
        }}
        onCreate={({ editor }) => {
          editerRef.current = editor
          if (editorRef) {
            editorRef.current = editor
          }
        }}
        content={description}
        parseOptions={{
          preserveWhitespace: true,
        }}
        editorProps={{
          attributes: {
            class:
              'max-h-[500px] transition-colors bg-secondary placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm rounded-md min-h-[200px] overflow-y-auto  px-3 py-2 focus:outline-none',
          },
          handlePaste: (view, event) => {
            const text = event.clipboardData?.getData('text/plain') || ''

            // 如果没有换行符，让默认处理器处理
            if (!text.includes('\n')) {
              return false
            }

            event.preventDefault()

            // 标准化换行符，避免不同系统的换行符差异
            const normalizedText = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n')

            // 获取编辑器实例
            const editor = (view as any).editor
            if (!editor) {
              return false
            }

            // 分割文本行
            const lines = normalizedText.split('\n')

            // 构建要插入的内容数组
            const contentParts: any[] = []

            lines.forEach((line, index) => {
              // 处理话题标签
              const topicMatches = line.match(pasteRegex)

              if (topicMatches && isSupportTopic) {
                // 处理包含话题标签的行
                let remainingText = line

                // 按顺序处理每个话题标签
                topicMatches.forEach((match) => {
                  const matchIndex = remainingText.indexOf(match)

                  // 添加话题标签前的文本
                  if (matchIndex > 0) {
                    const beforeText = remainingText.substring(0, matchIndex)
                    if (beforeText.trim()) {
                      contentParts.push({
                        type: 'text',
                        text: beforeText
                      })
                    }
                  }

                  // 添加话题节点
                  const topicText = match.replace('#', '').trim()
                  contentParts.push({
                    type: 'topic',
                    attrs: { text: topicText }
                  })

                  // 更新剩余文本
                  remainingText = remainingText.substring(matchIndex + match.length)
                })

                // 添加最后剩余的文本
                if (remainingText.trim()) {
                  contentParts.push({
                    type: 'text',
                    text: remainingText
                  })
                }
              } else {
                // 没有话题标签，直接添加文本
                if (line.trim() || index === 0) { // 保留第一行即使为空，其他空行跳过
                  contentParts.push({
                    type: 'text',
                    text: line
                  })
                }
              }

              // 除了最后一行，都添加硬换行
              if (index < lines.length - 1) {
                contentParts.push({
                  type: 'hardBreak'
                })
              }
            })

            // 使用编辑器的插入命令
            editor.commands.insertContent(contentParts)

            return true
          },
        }}
      />
      {isSupportTopic && (
        <div className="flex gap-3">
          <TooltipButton
            variant="outline"
            size="sm"
            tooltip={
              <div>
                在“#”后输入话题，空格或回车完成创建
                <img src={TopicTutorialGif} alt="话题教程" className="mb-1 mt-2 h-36 rounded-lg" />
              </div>
            }
            onClick={insertTopic}
          >
            #添加话题
          </TooltipButton>
          <TopicsDialog
            onSubmit={(value) => {
              if (!value.length) return
              // 增加话题
              value.forEach((item) => {
                editerRef.current?.commands.addTopic({ text: `${item}` })
              })
            }}
          />
          {onSyncB && (
            <Button size="sm" variant="outline" onClick={onSyncB}>
              话题同步至B类平台
            </Button>
          )}
        </div>
      )}
    </div>
  )
}
