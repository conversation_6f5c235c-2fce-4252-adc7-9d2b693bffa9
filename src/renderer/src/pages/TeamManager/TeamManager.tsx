import { Button } from '@renderer/shadcn-components/ui/button'
import { useEffect, useMemo, useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import type { PaginationState } from '@tanstack/react-table'
import { useTeamService } from '@renderer/infrastructure/services'
import type { Member } from '@renderer/infrastructure/model'
import { RoleType } from '@renderer/infrastructure/model'
import { InviteMember } from '@renderer/pages/TeamManager/components/member/InviteMember'
import { useConfirm } from '@renderer/hooks/useConfirm'
import { useNotify } from '@renderer/hooks/use-notify'
import { AccountIdSelectorSheet } from '@renderer/components/AccountSelector/account-id-selector-sheet'
import { useMessageStore } from '@renderer/store/messageStore'
import { EditRemark } from '@renderer/pages/TeamManager/components/member/edit-remark'
import { DataTable } from '@renderer/components/table/data-table'
import { TeamTop } from './components/team/teamTop'
import { useContextStore } from '@renderer/store/contextStore'
import { getTableColumns } from './components/member/columns'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@renderer/components/Tabs'

import { TeamPlugins } from './TeamPlugins'

export function TeamManager({ tabsDefaultValue = 'member' }: { tabsDefaultValue?: string }) {
  const teamService = useTeamService()
  const { confirm } = useConfirm()
  const { notifyService } = useNotify()
  const queryClient = useQueryClient()

  const [openInviteMember, setOpenInviteMember] = useState(false)
  const [openSetAccountOperator, setOpenSetAccountOperator] = useState(false)
  const [operatingAccountIds, setOperatingAccountIds] = useState<string[]>([])

  const [openRemark, setOpenRemark] = useState(false)

  // 成员的用户 id, 设置成员的运营账号
  /**
   * @deprecated 应该使用member，而不是memberUserId
   */
  const [memberUserId, setMemberUserId] = useState('')
  // 成员的用户，设置成员的运营账号
  const [member, setMember] = useState<Member | null>(null)

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })

  const userInfo = useContextStore((state) => state.userInfo)

  const columns = useMemo(
    () =>
      getTableColumns(
        userInfo,
        async (memberId: string) => {
          const accounts = await teamService.getMemberAccounts(memberId)
          setOperatingAccountIds(accounts)
          // 设置运营账号
          setMemberUserId(memberId)
          setOpenSetAccountOperator(true)
        },
        async (id: string, role: string) => {
          console.log('id:\n', `${id}: 设为/取消 管理员`)

          // 设为/取消 管理员
          if (
            await confirm({
              title: '身份设置',
              description: `确定${role === RoleType.Admin ? '取消他管理员身份' : '将他设为管理员'}吗?`,
              confirmText: `确定${role === RoleType.Admin ? '取消' : ''}`,
              cancelText: '取消',
            })
          ) {
            console.log('@@@:\n', '确定')

            const param = role === RoleType.Admin ? RoleType.Member : RoleType.Admin
            void teamService.setMemberRoles(id, [param]).then(() => {
              void queryClient.invalidateQueries({
                queryKey: ['members'],
              })
            })
          } else {
            console.debug('cancelled')
          }
        },
        async (id: string) => {
          // 移除成员
          if (
            await confirm({
              title: '移除成员',
              type: 'destructive',
              description: '确定移除成员?',
              confirmText: '确定移除',
              cancelText: '取消',
            })
          ) {
            void teamService.removeMember(id).then(() => {
              notifyService.success('操作成功')
              void queryClient.invalidateQueries({
                queryKey: ['members'],
              })
            })
          } else {
            console.debug('cancelled')
          }
        },
        async (id: string) => {
          if (
            await confirm({
              title: '恢复成员',
              description: `团队权益发生变化，需恢复该成员的使用吗？`,
              confirmText: '恢复',
              cancelText: '取消',
              // type: 'destructive',
            })
          ) {
            await teamService.unFreezeMembers(id)
            await queryClient.invalidateQueries({
              queryKey: ['members'],
            })
          }
        },
        async (member: Member) => {
          setMember(member)
          setOpenRemark(true)
        },
      ),
    [confirm, notifyService, queryClient, teamService, userInfo],
  )

  const query = useQuery({
    queryKey: ['members', pagination],
    queryFn: () =>
      teamService.getMembers({
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      }),
  })

  /**
   * 邀请成员
   */
  const invite = () => {
    setOpenInviteMember(true)
  }

  /**
   * 设置运营账号
   */
  const onSaveSetAccountOperator = (accountIds: string[]) => {
    void teamService.setMemberAccounts(memberUserId, accountIds).then(() => {
      notifyService.success('设置运营账号成功')
      void queryClient.invalidateQueries({
        queryKey: ['members'],
      })
    })
  }

  const { shouldRefresh, setShouldRefresh } = useMessageStore((state) => ({
    shouldRefresh: state.shouldRefresh,
    setShouldRefresh: state.setShouldRefresh,
  }))
  useEffect(() => {
    if (shouldRefresh) {
      void queryClient.invalidateQueries({
        queryKey: ['members'],
      })
      setShouldRefresh(false)
    }
  }, [queryClient, setShouldRefresh, shouldRefresh])

  return (
    <div className="flex h-full overflow-hidden">
      <div className="electron-drag-region flex flex-1 gap-5 overflow-hidden rounded-xl bg-white px-5 pb-5 pt-2.5 shadow-sm">
        <div className="flex flex-1 flex-col overflow-hidden">
          {/* 页面标题区域 */}
          <div
            className="electron-drag-region mb-4 flex items-center justify-between"
            style={{ marginTop: '6px' }}
          >
            <span className="drag-handler text-base font-medium">团队管理</span>
          </div>

          {/* 团队信息区域 - 移动到tabs上方 */}
          <div className="mb-[12px]">
            <TeamTop />
          </div>

          <Tabs defaultValue={tabsDefaultValue} className="flex h-full flex-col overflow-hidden">
            <div className="electron-drag">
              <div className="electron-drag-region flex items-center justify-between border-b">
                <TabsList className="w-auto flex-shrink-0 border-none">
                  <TabsTrigger value="member">
                    <span className="text-[16px]">成员</span>
                  </TabsTrigger>
                  <TabsTrigger value="plugins">
                    <span className="text-[16px]">团队插件</span>
                  </TabsTrigger>
                </TabsList>
                <Button onClick={invite}>邀请成员</Button>
              </div>
            </div>
            <TabsContent value="member" className={'flex-1 overflow-auto pt-6'}>
              <DataTable
                columns={columns}
                data={query.data?.data ?? []}
                rowCount={query.data?.totalSize ?? 0}
                pagination={pagination}
                setPagination={setPagination}
              />
            </TabsContent>
            <TabsContent value={'plugins'} className={'flex-1 overflow-hidden pt-6'}>
              <TeamPlugins />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* 邀请成员 */}
      <InviteMember open={openInviteMember} setOpen={setOpenInviteMember} />

      {/* 设置运营账号 */}
      <AccountIdSelectorSheet
        open={openSetAccountOperator}
        selectedAccountIds={operatingAccountIds}
        onOpenChange={setOpenSetAccountOperator}
        onChange={onSaveSetAccountOperator}
        selectable={(x) => x.operable}
      />

      <EditRemark
        open={openRemark}
        member={member}
        onOpenChange={setOpenRemark}
        onConfirmed={() => {
          void queryClient.invalidateQueries({
            queryKey: ['members'],
          })
        }}
      />
    </div>
  )
}
