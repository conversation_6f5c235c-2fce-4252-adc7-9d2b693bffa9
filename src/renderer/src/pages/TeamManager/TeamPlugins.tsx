import { Button } from '@renderer/shadcn-components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@renderer/shadcn-components/ui/dialog'
import { Input } from '@renderer/shadcn-components/ui/input'
import { Switch } from '@renderer/shadcn-components/ui/switch'
import SuperIcon from '@renderer/assets/team/super.png'
import { useEffect, useState } from 'react'
import { useTeamService } from '@renderer/infrastructure/services'
import { useQuery } from '@tanstack/react-query'
import { useCurrentTeam } from '@renderer/hooks/use-current-team'
import { toast } from 'sonner'
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@renderer/shadcn-components/ui/sheet'
import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import help1 from '@renderer/assets/super/1.png'
import help2 from '@renderer/assets/super/2.png'
import help3 from '@renderer/assets/super/3.png'
import { useInnerContextStore } from '@renderer/store/contextStore'

export function TeamPlugins() {
  const team = useCurrentTeam()
  const teamService = useTeamService()

  const [open, setOpen] = useState(false)
  const [qdyOpen, setQdyOpen] = useState(false)
  const [superdir, setSuperdir] = useState<{
    id: string
    name: string
    enabled: boolean
    componentArgs: Record<string, unknown>
  }>({
    componentArgs: { token: '' },
    enabled: false,
    name: '',
    id: '',
  })

  const [qingdouyun, setQingdouyun] = useState<{
    id: string
    name: string
    enabled: boolean
    componentArgs: Record<string, unknown>
  }>({
    componentArgs: { token: '' },
    enabled: false,
    name: '',
    id: '',
  })

  const { data: currentTeam } = useQuery({
    queryKey: ['getTeamDetail-plugins'],
    queryFn: () => {
      return teamService.getTeamDetail(team.id)
    },
    initialData: null,
  })

  useEffect(() => {
    const superdir = currentTeam?.components?.find((c) => c.name === 'superdir')
    setSuperdir(superdir ?? { componentArgs: { token: '' }, enabled: false, id: '', name: '' })

    const qingdouyun = currentTeam?.components?.find((c) => c.name === 'qingdouyun')
    setQingdouyun(qingdouyun ?? { componentArgs: { token: '' }, enabled: false, id: '', name: '' })
  }, [currentTeam])

  return (
    <div>
      <div className="flex flex-col gap-3 rounded-lg border border-[#E4E6EB] p-4">
        <div className="flex items-center justify-between">
          <img src={SuperIcon} alt="" className="h-[22px]" />
          <Switch
            checked={superdir.enabled}
            onCheckedChange={(check) => {
              teamService.setTeamPlugin(team.id, {
                name: 'superdir',
                componentArgs: {
                  token: superdir.componentArgs.token,
                },
                enable: check,
              })

              useInnerContextStore.getState().setCurrentTeam({
                ...team,
                components: (team.components || []).map((c) => {
                  if (c.name === 'superdir') {
                    return {
                      ...c,
                      enabled: check,
                    }
                  }
                  return c
                }),
              })

              setSuperdir({
                ...superdir,
                enabled: check,
              })
            }}
          />
        </div>
        <div>
          超级编导是围绕短视频生态打造的集创意脚本、批量剪辑合成、视频分发为一体的创意生产平台。
        </div>
        <div className="flex gap-2">
          <Dialog onOpenChange={setOpen} open={open}>
            <DialogTrigger asChild>
              <Button>设置</Button>
            </DialogTrigger>
            <DialogContent className="w-[480px] bg-white">
              <DialogHeader>
                <DialogTitle>设置「超级编导」123</DialogTitle>
                <DialogDescription></DialogDescription>
              </DialogHeader>
              <Input
                value={superdir.componentArgs.token as string}
                placeholder="请输入帐号token"
                onChange={(e) => {
                  setSuperdir({
                    ...superdir,
                    componentArgs: {
                      ...superdir.componentArgs,
                      token: e.target.value,
                    },
                  })
                }}
              />
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">取消</Button>
                </DialogClose>
                <Button
                  onClick={async () => {
                    const enable = superdir.enabled
                    await teamService.setTeamPlugin(team.id, {
                      name: 'superdir',
                      componentArgs: {
                        token: superdir.componentArgs.token,
                      },
                      enable: enable,
                    })

                    setSuperdir({
                      ...superdir,
                      enabled: enable,
                    })

                    useInnerContextStore.getState().setCurrentTeam({
                      ...team,
                      components: (team.components || []).map((c) => {
                        if (c.name === 'superdir') {
                          return {
                            ...c,
                            componentArgs: {
                              ...c.componentArgs,
                              token: superdir.componentArgs.token,
                            },
                          }
                        }
                        return c
                      }),
                    })
                    toast.success('保存成功')
                    setOpen(false)
                  }}
                >
                  保存
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline">如何使用?</Button>
            </SheetTrigger>
            <SheetContent className="flex w-[560px] !max-w-none flex-col overflow-hidden bg-white">
              <SheetHeader>
                <SheetTitle>如何绑定「超级编导」帐号?</SheetTitle>
              </SheetHeader>
              <ScrollArea className="w-full flex-1">
                <div className="flex flex-col gap-2">
                  <img src={help3} alt={'aboutImg'} />
                  <div className="flex items-center justify-center">
                    <span>{'1. 打开<超级编导>官网下载客户端'}</span>
                    <span
                      className="cursor-pointer text-[#4F46E5]"
                      onClick={() => {
                        window.open('https://www.superdir.cn')
                      }}
                    >
                      https://www.superdir.cn
                    </span>
                  </div>
                  <img src={help2} alt={'aboutImg'} />
                  <div className="flex items-center justify-center">
                    {'2. 注册并在【团队概况】-【团队资料信息】【复制token】'}
                  </div>
                  <img src={help1} alt={'aboutImg'} />
                  <div className="flex items-center justify-center">
                    {'3. 绑定【超级编导】账号 粘贴token 完成绑定操作'}
                  </div>
                </div>
              </ScrollArea>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      <div className="flex flex-col gap-3 mt-4 rounded-lg border border-[#E4E6EB] p-4">
        <div className="flex items-center justify-between">
          <img src={SuperIcon} alt="" className="h-[22px]" />
          <Switch
            checked={qingdouyun.enabled}
            onCheckedChange={(check) => {
              teamService.setTeamPlugin(team.id, {
                name: 'qingdouyun',
                componentArgs: {
                  token: qingdouyun.componentArgs.token,
                },
                enable: check,
              })

              useInnerContextStore.getState().setCurrentTeam({
                ...team,
                components: (team.components || []).map((c) => {
                  if (c.name === 'qingdouyun') {
                    return {
                      ...c,
                      enabled: check,
                    }
                  }
                  return c
                }),
              })

              setQingdouyun({
                ...qingdouyun,
                enabled: check,
              })
            }}
          />
        </div>
        <div>
          青豆云是围绕短视频生态打造的集创意脚本、批量剪辑合成、视频分发为一体的创意生产平台。
        </div>
        <div className="flex gap-2">
          <Dialog onOpenChange={setQdyOpen} open={qdyOpen}>
            <DialogTrigger asChild>
              <Button>设置</Button>
            </DialogTrigger>
            <DialogContent className="w-[480px] bg-white">
              <DialogHeader>
                <DialogTitle>设置「青豆云」</DialogTitle>
                <DialogDescription></DialogDescription>
              </DialogHeader>
              <Input
                value={qingdouyun.componentArgs.token as string}
                placeholder="请输入帐号token"
                onChange={(e) => {
                  setQingdouyun({
                    ...qingdouyun,
                    componentArgs: {
                      ...qingdouyun.componentArgs,
                      token: e.target.value,
                    },
                  })
                }}
              />
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">取消</Button>
                </DialogClose>
                <Button
                  onClick={async () => {
                    const enable = qingdouyun.enabled
                    await teamService.setTeamPlugin(team.id, {
                      name: 'qingdouyun',
                      componentArgs: {
                        token: qingdouyun.componentArgs.token,
                      },
                      enable: enable,
                    })

                    setQingdouyun({
                      ...qingdouyun,
                      enabled: enable,
                    })

                    useInnerContextStore.getState().setCurrentTeam({
                      ...team,
                      components: (team.components || []).map((c) => {
                        if (c.name === 'qingdouyun') {
                          return {
                            ...c,
                            componentArgs: {
                              ...c.componentArgs,
                              token: qingdouyun.componentArgs.token,
                            },
                          }
                        }
                        return c
                      }),
                    })
                    toast.success('保存成功')
                    setQdyOpen(false)
                  }}
                >
                  保存
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline">如何使用?</Button>
            </SheetTrigger>
            <SheetContent className="flex w-[560px] !max-w-none flex-col overflow-hidden bg-white">
              <SheetHeader>
                <SheetTitle>如何绑定「青豆云」帐号?</SheetTitle>
              </SheetHeader>
              <ScrollArea className="w-full flex-1">
                <div className="flex flex-col gap-2">
                  <img src={help3} alt={'aboutImg'} />
                  <div className="flex items-center justify-center">
                    <span>{'1. 打开<青豆云>官网下载客户端'}</span>
                    <span
                      className="cursor-pointer text-[#4F46E5]"
                      onClick={() => {
                        window.open('https://www.qingdouyun.cn')
                      }}
                    >
                      https://www.qingdouyun.cn
                    </span>
                  </div>
                  <img src={help2} alt={'aboutImg'} />
                  <div className="flex items-center justify-center">
                    {'2. 注册并在【团队概况】-【团队资料信息】【复制token】'}
                  </div>
                  <img src={help1} alt={'aboutImg'} />
                  <div className="flex items-center justify-center">
                    {'3. 绑定【青豆云】账号 粘贴token 完成绑定操作'}
                  </div>
                </div>
              </ScrollArea>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>
  )
}
