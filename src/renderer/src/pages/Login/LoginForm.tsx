import { Input } from '@renderer/shadcn-components/ui/input'
import { Button } from '@renderer/shadcn-components/ui/button'
import PhoneIcon from '@renderer/assets/passport/phone.svg?react'
import VerifyIcon from '@renderer/assets/passport/verify.svg?react'
import PasswordIcon from '@renderer/assets/passport/password.svg?react'
import { Checkbox } from '@renderer/shadcn-components/ui/checkbox'
import { FormItem as LoginFormItem } from '@renderer/pages/Login/FormItem'
// import { Head2, Paragraph, Terms } from '@renderer/pages/Login/Terms'
import { useEffect, useState } from 'react'
import { useNotify } from '@renderer/hooks/use-notify'
import { useNavigate } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { useLoginService, useGlobalStorageService } from '@renderer/infrastructure/services'
import { useValidation } from '@renderer/hooks/validation/validation'
import { animated, useSpring } from '@react-spring/web'
import { Tabs, TabsList, TabsTrigger } from '@renderer/shadcn-components/ui/tabs'
import { PasswordInput } from '@renderer/components/PasswordInput'
import { alertBaseManager } from '@renderer/components/alertBase'
import { toast } from 'sonner'

const phoneNumberValidator = Validator.of<string>().addRule((subject) => {
  const phoneNumberRegex = /^(1[3-9][0-9])\d{8}$/
  if (!phoneNumberRegex.test(subject)) {
    return new RuleResult('invalid', '手机号码格式不正确')
  }
  return RuleResult.valid
})

const verifyCodeValidator = Validator.of<string>().addRule((subject) => {
  const verifyCodeRegex = /^\d{6}$/
  if (!verifyCodeRegex.test(subject)) {
    return new RuleResult('invalid', '验证码格式不正确')
  }
  return RuleResult.valid
})

const dummyValidator = Validator.of<string>().addRule(() => RuleResult.valid)

const passwordValidator = Validator.of<string>().addRule((subject) => {
  if (!subject) {
    return new RuleResult('invalid', '请输入密码')
  }
  return RuleResult.valid
})

export function RenderLoginForm() {
  const springs = useSpring({
    from: { opacity: 0 },
    to: { opacity: 1 },
    config: { duration: 200 },
    delay: 300,
  })

  const loginService = useLoginService()
  const globalStorageService = useGlobalStorageService()
  const navigate = useNavigate()
  const { notifyService } = useNotify()

  const [phoneNumber, setPhoneNumber] = useState('')
  const [verifyCode, setVerifyCode] = useState('')
  const [password, setPassword] = useState('')

  const [accepted, setAccepted] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [rememberPassword, setRememberPassword] = useState(false)

  const [tab, setTab] = useState<'code' | 'password'>('password')

  const isCode = tab === 'code'

  const { mutate: loginMutate, isPending } = useMutation({
    mutationFn: () =>
      loginService.loginV2(
        tab === 'code'
          ? { username: phoneNumber, code: verifyCode }
          : { username: phoneNumber, password },
      ),
    onSuccess: () => {
      // 保存登录偏好
      if (tab === 'password') {
        globalStorageService.updatePasswordLoginPreferences(
          phoneNumber,
          rememberPassword,
          rememberPassword ? password : undefined
        )

        // 兼容旧的密码记忆功能
        if (rememberPassword) {
          globalStorageService.setRememberedPassword(phoneNumber, password)
        } else {
          globalStorageService.clearRememberedPassword(phoneNumber)
        }
      } else {
        globalStorageService.updateCodeLoginPreferences(phoneNumber)
      }

      // 兼容旧的手机号记忆功能
      if (tab === 'code') {
        globalStorageService.setLastUsedPhone(phoneNumber)
      } else {
        globalStorageService.setLastUsedUsername(phoneNumber)
      }

      navigate('/newMain')
    },
    onError: (error) => {
      console.error(error)
      toast.error(error.message)
    },
  })

  const isWaiting = countdown > 0

  const { conclusion: phoneNumberConclusion } = useValidation(phoneNumber, phoneNumberValidator)

  const { conclusion: verifyCodeConclusion } = useValidation(
    verifyCode,
    isCode ? verifyCodeValidator : dummyValidator,
  )
  const { conclusion: passwordConclusion } = useValidation(
    password,
    isCode ? dummyValidator : passwordValidator,
  )

  const canSendVerifyCode = phoneNumberConclusion.valid && !isWaiting

  const canLogin = verifyCodeConclusion.valid && passwordConclusion.valid && accepted && !isPending

  // 初始化时自动填充登录偏好和协议同意状态
  useEffect(() => {
    // 获取登录偏好
    const loginPreferences = globalStorageService.getLoginPreferences()

    if (loginPreferences) {
      // 设置上次使用的登录方式
      setTab(loginPreferences.lastLoginMethod)

      // 根据登录方式回显对应的数据
      if (loginPreferences.lastLoginMethod === 'password') {
        const { phoneNumber, rememberPassword, password } = loginPreferences.passwordLogin
        if (phoneNumber) {
          setPhoneNumber(phoneNumber)
          setRememberPassword(rememberPassword)
          if (rememberPassword && password) {
            setPassword(password)
          }
        }
      } else {
        const { phoneNumber } = loginPreferences.codeLogin
        if (phoneNumber) {
          setPhoneNumber(phoneNumber)
        }
      }
    } else {
      // 没有登录偏好，使用默认设置
      setTab('password')
    }

    // 自动加载协议同意状态
    const termsAccepted = globalStorageService.getTermsAccepted()
    if (termsAccepted) {
      setAccepted(true)
    }
  }, [globalStorageService])

  // 当标签页切换时，回显对应登录方式的数据
  useEffect(() => {
    const loginPreferences = globalStorageService.getLoginPreferences()

    if (loginPreferences) {
      if (tab === 'password') {
        const { phoneNumber, rememberPassword, password } = loginPreferences.passwordLogin
        if (phoneNumber) {
          setPhoneNumber(phoneNumber)
          setRememberPassword(rememberPassword)
          if (rememberPassword && password) {
            setPassword(password)
          } else {
            setPassword('')
          }
        } else {
          // 如果没有保存的密码登录数据，清空相关字段
          setPassword('')
          setRememberPassword(false)
        }
      } else {
        const { phoneNumber } = loginPreferences.codeLogin
        if (phoneNumber) {
          setPhoneNumber(phoneNumber)
        }
        // 切换到验证码登录时，清空密码相关状态
        setPassword('')
        setRememberPassword(false)
        setVerifyCode('')
      }
    } else {
      // 没有登录偏好时，清空所有字段
      if (tab === 'password') {
        setPassword('')
        setRememberPassword(false)
      } else {
        setVerifyCode('')
      }
    }
  }, [tab, globalStorageService])

  // 当协议同意状态变化时，自动保存
  useEffect(() => {
    globalStorageService.setTermsAccepted(accepted)
  }, [accepted, globalStorageService])

  useEffect(() => {
    if (countdown === 0) {
      return
    }

    const timeout = setTimeout(() => {
      setCountdown((countdown) => countdown - 1)
    }, 1000)

    return () => clearInterval(timeout)
  }, [countdown])

  async function sendVerifyCode() {
    if (!canSendVerifyCode) {
      return
    }

    // 防止重复运行
    if (isWaiting) {
      return
    }

    const non_production_code = await loginService.sendVerifyCode(phoneNumber)

    if (non_production_code) {
      notifyService.info(`测试验证码：${non_production_code}`)
      setVerifyCode(non_production_code)
    }

    setCountdown(() => 60)
  }
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === 'Enter' && canLogin) {
        loginMutate()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [canLogin, loginMutate])
  return (
    <animated.div style={springs}>
      <div className="mt-8 flex w-full flex-col items-center gap-4">
        <Tabs value={tab} onValueChange={(value) => setTab(value as 'code' | 'password')}>
          <TabsList>
            <TabsTrigger value="password">密码登录</TabsTrigger>
            <TabsTrigger value="code">验证码登录</TabsTrigger>
          </TabsList>
        </Tabs>
        <div className="grid w-[358px] grid-cols-1 gap-4">
          <LoginFormItem prepend={<PhoneIcon />}>
            <Input
              type="text"
              placeholder={tab === 'code' ? '手机号码' : '手机号码/账号'}
              className="h-[54px] bg-background/50 pl-10"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
            />
          </LoginFormItem>
          {tab === 'password' ? (
            <LoginFormItem prepend={<PasswordIcon />}>
              <PasswordInput
                asChild
                placeholder="密码"
                className="h-[54px] bg-background/50 pl-10"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </LoginFormItem>
          ) : (
            <LoginFormItem
              prepend={<VerifyIcon />}
              append={
                <Button variant="link" onClick={sendVerifyCode} disabled={!canSendVerifyCode}>
                  {isWaiting ? `重新发送(${countdown})秒` : '获取验证码'}
                </Button>
              }
            >
              <Input
                type="text"
                placeholder="验证码"
                className="h-[54px] bg-background/50 pl-10 pr-28"
                value={verifyCode}
                onChange={(e) => setVerifyCode(e.target.value)}
              />
            </LoginFormItem>
          )}
          <div className="flex h-5 items-center justify-between">
            {tab === 'password' ? (
              <>
                <LoginFormItem className="gap-1">
                  <Checkbox
                    id="remember"
                    checked={rememberPassword}
                    onClick={() => setRememberPassword(!rememberPassword)}
                  />
                  <label
                    htmlFor="remember"
                    className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    记住密码
                  </label>
                </LoginFormItem>
                <Button
                  variant="link"
                  className="h-5 p-0 text-primary hover:no-underline"
                  onClick={() => {
                    alertBaseManager.open({
                      title: '忘记密码',
                      description: '请先用验证码登录后，在个人设置中重置密码。',
                      buttons: [],
                      okText: '我知道了',
                    })
                  }}
                >
                  忘记密码
                </Button>
              </>
            ) : (
              // 验证码登录模式下的占位空间，保持与密码登录相同的高度
              <div className="w-full"></div>
            )}
          </div>
        </div>

        <div className="flex-0 mt-10 h-max w-full">
          <Button className="h-12 w-full" disabled={!canLogin} onClick={() => loginMutate()}>
            登录
          </Button>
        </div>

        <div className="mt-0 flex h-0 w-full items-center justify-start">
          <LoginFormItem className="gap-1">
            <Checkbox id="terms" checked={accepted} onClick={() => setAccepted(!accepted)} />
            <label
              htmlFor="terms"
              className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              我已阅读并接受
              <span className="text-primary">
                《
                <Button
                  variant="link"
                  onClick={() => {
                    window.open(
                      'https://lite-download.yixiaoer.cn/privacy/yixiaoeruseragreement.pdf',
                    )
                  }}
                  className="p-0"
                >
                  用户服务协议
                </Button>
                》
              </span>
              和
              <span className="text-primary">
                《
                <Button
                  variant="link"
                  onClick={() => {
                    window.open(
                      'https://lite-download.yixiaoer.cn/privacy/yixiaoerprivacypolicy.pdf',
                    )
                  }}
                  className="p-0"
                >
                  隐私政策
                </Button>
                》
              </span>
            </label>
          </LoginFormItem>
        </div>
      </div>
    </animated.div>
  )
}
