import type { SessionDetectionResult } from '@common/structure/session-detection-result'
import type {
  LocalAuditStatus,
  InnerPushingResult,
  InnerQueryStateResult,
  QueryStateResult,
  PublishOverviewRawItem,
  PublishOverviewHeaderItem,
  OverviewContentType,
  SessionState,
  AccountSession,
} from '@common/structure'
import { PushingError } from '@main/model/pushing-error'
import type { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { mockAuditStateQuery, mockPushing, mockSessionDetect } from '@main/services/mockConfig'
import type { AccountService, DataColumn } from '@yixiaoer/platform-service'
import { auditStatusEnum, Platforms } from '@yixiaoer/platform-service'
import type { BaseResult } from '@yixiaoer/platform-service/dist/media-platform/baseResult'
import { EventEmitter } from 'events'
import { getPlatformServicePromise } from '@main/services/platform-service/import-promise'
import { platformNames } from '@common/model/platform-name'
import { EditContentType } from '@common/model/content-type'
import type { PubContentEnum } from '@yixiaoer/platform-service/dist/cloud/model/TypeEnum'
import type { AccountInfoStructure } from '@common/model/account-info'

const resultCode = {
  success: 0,
  request_error: 172001,
  io_error: 172002,
}

export function platformNameConverter(platformName: string): Platforms {
  switch (platformName) {
    case platformNames.DouYin:
      return Platforms.DouYin
    case platformNames.KuaiShou:
      return Platforms.KuaiShou
    case platformNames.XiaoHongShu:
      return Platforms.XiaoHongShu
    case platformNames.WeiXinShiPinHao:
      return Platforms.ShiPinHao
    case platformNames.BiliBili:
      return Platforms.BiLiBiLi
    case platformNames.XinLangWeiBo:
      return Platforms.XinLangWeiBo
    case platformNames.TouTiaoHao:
      return Platforms.TouTiaoHao
    case platformNames.BaiJiaHao:
      return Platforms.BaiJiaHao
    case platformNames.XiGuaShiPin:
      return Platforms.XiGuaShiPin
    case platformNames.ZhiHu:
      return Platforms.ZhiHu
    case platformNames.QiEHao:
      return Platforms.QiEHao
    case platformNames.SouHuHao:
      return Platforms.SouHuHao
    case platformNames.YiDianHao:
      return Platforms.YiDianHao
    case platformNames.DaYuHao:
      return Platforms.DaYuHao
    case platformNames.WangYiHao:
      return Platforms.WangYiHao
    case platformNames.AiQiYi:
      return Platforms.AiQiYi
    case platformNames.TengXunWeiShi:
      return Platforms.TengXunWeiShi
    default:
      throw new Error(`未知平台转换: ${platformName}`)
  }
}

export function PubContentEnumConverter(contentType: EditContentType) {
  switch (contentType) {
    case EditContentType.Video:
      return 'video' as PubContentEnum
    case EditContentType.ImageText:
      return 'dynamic' as PubContentEnum
    case EditContentType.Article:
      return 'article' as PubContentEnum
    default:
      throw new Error(`未知内容类型转换: ${contentType}`)
  }
}

export abstract class PlatformService {
  constructor(protected platformName: string) {}

  abstract getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo>

  protected convertCookie(cookies: Electron.Cookie[]): string {
    try {
      const result = cookies.map((x) => `${x.name}=${x.value}`)
      return result.join('; ')
    } catch (e) {
      throw new Error('Cookie 转换失败')
    }
  }

  protected convertCookieObject(cookies: Electron.Cookie[]): string {
    try {
      return JSON.stringify(cookies)
    } catch (e) {
      throw new Error('Cookie 转换失败')
    }
  }

  protected convertAuditStatus(auditStatus: number): LocalAuditStatus {
    switch (auditStatus) {
      case 0:
        return '已发布'
      case 1:
        return '审核中'
      case 2:
        return '未通过'
      case 4:
        return '草稿'
      case 5:
        return '定时发布'
      case 7:
        return '转码中'
      case 10:
        return '用户撤回，下线，删除'
      case 11:
        return '不适合公开'
      case 13:
        return '非公开的'
      case 14:
        return '转码失败'
      default:
        throw new Error(`未知审核状态: ${auditStatus}`)
    }
  }

  protected async getData<TPlatformResult extends BaseResult, TBodyResult>(
    platformDataGetter: () => TPlatformResult | PromiseLike<TPlatformResult>,
    bodyDataGetter: (platformResult: TPlatformResult) => TBodyResult | PromiseLike<TBodyResult>,
  ): Promise<TBodyResult> {
    const response = await platformDataGetter()
    if (response.code !== resultCode.success) {
      return Promise.reject(new Error(`获取平台数据失败: ${response.msg}`))
    }
    return Promise.resolve(bodyDataGetter(response))
  }

  protected async convert2PlatformAccountInfo<TPlatformResult extends BaseResult>(
    platformDataGetter: () => TPlatformResult | PromiseLike<TPlatformResult>,
    platformAccountInfoGetter: (
      platformResult: TPlatformResult,
    ) => AuthorizingAccountInfo | PromiseLike<AuthorizingAccountInfo>,
  ): Promise<AuthorizingAccountInfo> {
    const response = await platformDataGetter()
    if (response.code !== resultCode.success) {
      throw new Error(`获取平台账号信息失败: ${response.msg}`)
    }
    return platformAccountInfoGetter(response)
  }

  pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
    wechatLockToken: [string, string] | null = null,
  ): Promise<string> {
    console.debug('未实现的发布方法', contentTypeName, authorId, body)
    throw new Error('未实现的发布方法')
  }

  pushDynamic(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
    wechatLockToken: [string, string] | null = null,
  ): Promise<string> {
    console.debug('未实现的发布方法', contentTypeName, authorId, body)
    throw new Error('未实现的发布方法')
  }

  pushArticle(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    console.debug('未实现的发布方法', contentTypeName, authorId, body)
    throw new Error('未实现的发布方法')
  }

  protected async getPushingResult<T extends InnerPushingResult>(
    getter: (eventEmitter: EventEmitter) => Promise<T>,
    progressCallBack: (progress: number, message: string) => void,
  ) {
    const eventEmitter = new EventEmitter()

    eventEmitter.on('publishing', (progress, message, _taskId) => {
      progressCallBack(progress, message)
    })

    let result: T
    if (process.env.NODE_ENV !== 'development' || !mockPushing) {
      result = await getter(eventEmitter)
    } else {
      // 模拟进度
      let progress = 0
      const interval = setInterval(() => {
        progressCallBack(progress, `正在发布${progress}`)
        progress += 10
        if (progress > 100) {
          clearInterval(interval)
        }
      }, 400)

      // 5秒后返回成功
      result = await new Promise<T>((resolve, _reject) => {
        setTimeout(() => {
          resolve({
            code: resultCode.success,
            msg: 'mock',
            publishId: 'mock_publishId',
          } as T)
        }, 5000)
      })
    }

    switch (result.code) {
      case resultCode.success:
        return result
      case resultCode.request_error:
        throw new PushingError(result.code, '网络异常，请重新发布', result.errorInfo)
      default:
        throw new PushingError(result.code, result.msg ?? '', result.errorInfo)
    }
  }

  async queryState(
    taskId: string,
    contentType: EditContentType,
    account: AccountInfoStructure,
    publishId: string,
  ): Promise<QueryStateResult> {
    const result = await (
      await getPlatformServicePromise()
    ).Cloud.checkAuditStatus(
      platformNameConverter(this.platformName),
      {
        platformAccountId: account.accountId,
        platformAccountName: account.displayName,
        platformAccountSpaceId: account.spaceId,
        platformAuthorId: account.authorId,
        parentId: account.parentAccountId,
      },
      taskId,
      publishId,
      PubContentEnumConverter(contentType),
    )

    return {
      detail: result?.msg || '',
      documentId: result?.docId ?? '',
      auditStatus: this.convertAuditStatus(result?.auditStatus ?? 1),
      createTime: new Date(result?.create_time ?? 0),
      openUrl: result?.openUrl ?? null,
    } satisfies QueryStateResult
  }

  protected async getQueryState<T extends InnerQueryStateResult>(
    getter: () => Promise<T>,
  ): Promise<QueryStateResult> {
    let result: T
    if (process.env.NODE_ENV !== 'development' || !mockAuditStateQuery) {
      result = await getter()
    } else {
      result = await new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: resultCode.success,
            msg: '发布成功',
            docId: 'docId',
            auditStatus: auditStatusEnum.published,
            create_time: Date.now(),
            openUrl: 'http://www.baidu.com',
          } as T)
        }, 3000)
      })
    }

    if (result.code !== resultCode.success) {
      throw new Error(result.msg)
    }
    if (
      result.docId === undefined ||
      result.auditStatus === undefined ||
      result.create_time === undefined
    ) {
      throw new Error(`查询结果格式不正确: ${result.msg}，详情：${JSON.stringify(result)}`)
    }
    return {
      detail: result.msg || '',
      documentId: result.docId,
      auditStatus: this.convertAuditStatus(result.auditStatus),
      createTime: new Date(result.create_time),
      openUrl: result.openUrl ?? null,
    } satisfies QueryStateResult
  }

  async sessionDetect(accountSession: AccountSession, ignoreMock = false) {
    if (process.env.NODE_ENV !== 'development' || !mockSessionDetect || ignoreMock) {
      const cookiesString = this.convertCookieObject(accountSession.cookies)
      const accountService: AccountService = new (await getPlatformServicePromise()).AccountService(
        cookiesString,
        platformNameConverter(this.platformName),
      )
      const result = await accountService.checkAccountAlive()
      switch (result) {
        case 1:
          return '正常' satisfies SessionState as SessionState
        case 0:
          return '已失效' satisfies SessionState as SessionState
        case -1:
          throw new Error('无法检测会话状态')
        default:
          throw new Error('未知检测结果')
      }
    }
    await new Promise((resolve) => {
      setTimeout(resolve, 3000)
    })
    return '正常' satisfies SessionState as SessionState // mock
  }

  protected async getSessionDetectResult(
    accountId: string,
    getter: () => Promise<BaseResult>,
  ): Promise<SessionDetectionResult> {
    let userInfoResult: BaseResult
    if (process.env.NODE_ENV !== 'development' || !mockSessionDetect) {
      userInfoResult = await getter()
    } else {
      await new Promise((resolve) => {
        setTimeout(resolve, 3000)
      })
      // mock时应该成功
      userInfoResult = {
        code: resultCode.success,
        msg: 'mock',
      }
    }
    try {
      if (userInfoResult.code === resultCode.success) {
        return {
          accountId: accountId,
          state: '正常',
          detail: userInfoResult,
        }
      }
      return {
        accountId: accountId,
        state: '已失效',
        detail: userInfoResult,
      }
    } catch (e) {
      return {
        accountId: accountId,
        state: '已失效',
        detail: e,
      }
    }
  }

  queryAccountOverview(cookies: Electron.Cookie[]): Promise<{
    video?: DataColumn[]
    dynamic?: DataColumn[]
    article?: DataColumn[]
  }> {
    throw new Error('未实现的账号统计查询')
  }

  async queryPublishOverviewsHeaders(
    platformName: string,
    contentType: OverviewContentType | undefined,
  ): Promise<PublishOverviewHeaderItem[]> {
    const platformService = await getPlatformServicePromise()
    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          platformNameConverter(platformName),
          true,
          '[]',
          contentType,
        ),
      (x) => x.headers || [],
    )
  }

  queryPublishOverview(cookies: Electron.Cookie[]): Promise<PublishOverviewRawItem[]> {
    throw new Error('未实现的发布统计查询')
  }
}
