import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { BilibiliVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/bilibili'
import type { CascadingPlatformDataItem, PlatformDataItem } from '@common/structure'
import { mapRecursive } from '@common/structure'
import { Platforms } from '@yixiaoer/platform-service'

class BilibiliPlatformService extends PlatformService {
  constructor() {
    super(platformNames.BiliBili)
  }

  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Bilibili.publishVideo(
          cookie,
          body as BilibiliVideoTaskInput,
          eventEmitter,
        ),
      progressCallBack,
    )

    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Bilibili.getBilibiliUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.BiliBili,
      info.mid.toString(),
      info.uname,
      info.face,
    )
  }

  async getTopics(
    cookies: Electron.Cookie[],
    categoryId: number,
    keyWord?: string,
  ): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Bilibili.getBilibiliVideoTopic(
          this.convertCookie(cookies),
          categoryId,
          keyWord,
        ),
      (x) => x.data?.topics ?? [],
    )

    return result.map((item) => ({
      id: item.topic_id.toString(),
      text: item.topic_name,
      raw: item,
    }))
  }

  async getFriends(cookies: Electron.Cookie[], keyWord?: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Bilibili.getBilibiliFriend(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.items ?? [],
    )

    return result.map((item) => ({
      id: item.uid,
      text: item.name,
      raw: item,
    }))
  }

  async getVideoCategory(cookies: Electron.Cookie[]): Promise<CascadingPlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Bilibili.getBilibiliVideoCategory(
          this.convertCookie(cookies),
        ),
      (x) => x.data?.type_list ?? [],
    )
    return mapRecursive(result, {
      id: (x) => x.id.toString(),
      name: (x) => x.name,
      children: (x) => x.children,
    })
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const data = await platformService.DataService.getAccountReport(Platforms.BiLiBiLi, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const videoOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.BiLiBiLi,
          false,
          cookie,
          'video',
        ),
      (x) => x.data || [],
    )
    const dynamicOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.BiLiBiLi,
          false,
          cookie,
          'dynamic',
        ),
      (x) => x.data || [],
    )
    return [...videoOverviews, ...dynamicOverviews]
  }
}

export const bilibiliPlatformService = new BilibiliPlatformService()
