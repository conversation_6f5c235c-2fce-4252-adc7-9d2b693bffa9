import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { YidianhaoVideoTaskInput } from '@yixiaoer/platform-service'
import type { IYiDianHaoArticleData } from '@yixiaoer/platform-service/dist/media-platform/yidianhao/yidianhao-article'
import { Platforms } from '@yixiaoer/platform-service'

class YiDianHaoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.YiDianHao)
  }
  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)

    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Yidianhao.publishVideo(
        cookie,
        body as YidianhaoVideoTaskInput,
        eventEmitter,
      )
    }, progressCallBack)

    return result.publishId!
  }

  async pushArticle(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Yidianhao.publishArticle(
        body as IYiDianHaoArticleData,
        cookie,
        eventEmitter,
      )
    }, progressCallBack)

    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Yidianhao.getYidianhaoUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.YiDianHao,
      info.id.toString(),
      info.media_name,
      info.media_pic,
    )
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    // 一点号没有子类型配置，只获取一种数据类型放到 video 字段中
    const data = await platformService.DataService.getAccountReport(Platforms.YiDianHao, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(Platforms.YiDianHao, false, cookie),
      (x) => x.data || [],
    )
  }
}

export const yiDianHaoPlatformService = new YiDianHaoPlatformService()
