import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import {
  Platforms,
  type KuaishouDynamicTaskInput,
  type KuaishouVideoTaskInput,
} from '@yixiaoer/platform-service'
import type { MusicPageWrapper, MusicPlatformDataItem, PlatformDataItem } from '@common/structure'

class KuaiShouPlatformService extends PlatformService {
  constructor() {
    super(platformNames.KuaiShou)
  }

  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Kuaishou.publishVideo(
          cookie,
          body as Ku<PERSON>houVideoTaskInput,
          eventEmitter,
        ),
      progressCallBack,
    )
    return result.publishId!
  }

  // 推送图文混编
  async pushArticle(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Kuaishou.publishArticle(
          cookie,
          body as KuaishouDynamicTaskInput,
          eventEmitter,
        ),
      progressCallBack,
    )
    return result.publishId!
  }

  // 推送图文
  async pushDynamic(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Kuaishou.publishDynamic(
          cookie,
          body as KuaishouDynamicTaskInput,
          eventEmitter,
        ),
      progressCallBack,
    )
    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Kuaishou.getKuaishouUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.KuaiShou,
      info.userId.toString(),
      info.userName,
      info.userAvatar,
    )
  }

  async getTopics(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Kuaishou.getKuaishouTopic(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.data?.tags ?? [],
    )

    return result.map((item) => ({
      id: item.tag.id.toString(),
      text: item.tag.name,
      raw: item,
    }))
  }

  async getFriends(cookies: Electron.Cookie[]): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Kuaishou.getKuaishouFriend(this.convertCookie(cookies)),
      (x) => x.data?.list ?? [],
    )

    return result.map((item) => ({
      id: item.userId.toString(),
      text: item.userName,
      raw: item,
    }))
  }

  async getLocations(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Kuaishou.getKuaishouLocation(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.locations ?? [],
    )

    return result.map((item) => ({
      id: item.id.toString(),
      text: item.title ?? '未知地点',
      raw: item,
    }))
  }

  async getMusicList(
    cookies: Electron.Cookie[],
    keyWord: string,
    nextPage?: string,
  ): Promise<MusicPageWrapper> {
    console.log('getMusicList', keyWord, nextPage)
    return await this.getData(
      async () =>
        (await getPlatformServicePromise()).Kuaishou.getKuaishouMusicList(
          this.convertCookie(cookies),
          keyWord,
          nextPage,
        ),
      (x) => {
        return {
          items:
            x.musicList?.map(
              (item) =>
                ({
                  id: item.musicId,
                  title: item.title,
                  artist: item.author,
                  duration: item.duration / 1000,
                  url: item.url[0].url,
                  raw: item,
                }) satisfies MusicPlatformDataItem,
            ) ?? [],
          nextPage: x.nextPage,
        }
      },
    )
  }
  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const data = await platformService.DataService.getAccountReport(Platforms.KuaiShou, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(Platforms.KuaiShou, false, cookie),
      (x) => x.data || [],
    )
  }
}

export const kuaiShouPlatformService = new KuaiShouPlatformService()
