import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { CascadingPlatformDataItem, PlatformDataItem } from '@common/structure'
import { mapRecursive } from '@common/structure'
import type { BaijiahaoMiniVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/baijiahao/baijiahao-mini-video'
import { Platforms, type BaijiahaoVideoTaskInput } from '@yixiaoer/platform-service'
import type { BaijiahaoArticleTaskInput } from '@yixiaoer/platform-service/dist/media-platform/baijiahao/baijiahao-article'
import { PushContentType } from '@common/model/content-type'

class BaiJiaHaoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.BaiJiaHao)
  }

  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const tokenStr: string = ''

    const result = await this.getPushingResult(async (eventEmitter) => {
      if (contentTypeName === 'verticalVideo') {
        return (await getPlatformServicePromise()).Baijiahao.publishMiniVideo(
          cookie,
          body as BaijiahaoMiniVideoTaskInput,
          eventEmitter,
          Number(authorId),
          tokenStr,
        )
      } else {
        return (await getPlatformServicePromise()).Baijiahao.publishVideo(
          cookie,
          body as BaijiahaoVideoTaskInput,
          eventEmitter,
          Number(authorId),
          tokenStr,
        )
      }
    }, progressCallBack)

    return result.publishId!
  }

  async pushArticle(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Baijiahao.publishArticle(
        cookie,
        Number(authorId),
        body as BaijiahaoArticleTaskInput,
        '',
        eventEmitter,
      )
    }, progressCallBack)

    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Baijiahao.getBaijiahaoUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.BaiJiaHao,
      info.user.app_id.toString(),
      info.user.name,
      info.user.avatar,
    )
  }

  async getTopics(
    cookies: Electron.Cookie[],
    keyword: string,
    title: string,
  ): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Baijiahao.getBaijiahaoTopicList(
          this.convertCookie(cookies),
          keyword,
          title,
        ),
      (x) => x.data?.hot ?? [],
    )

    return result.map((item) => ({
      id: item.id,
      text: item.title,
      raw: item,
    }))
  }

  async getLocations(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Baijiahao.getBaijiahaoLocationListBySearch(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.data ?? [],
    )
    return result.map((item) => ({
      id: item.id.toString(),
      text: item.name,
      raw: item,
    }))
  }

  async getCategoryList(
    cookies: Electron.Cookie[],
    contentType: PushContentType,
  ): Promise<CascadingPlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Baijiahao.getBaijiahaoCategoryList(
          this.convertCookie(cookies),
          contentType === PushContentType.Article ? 'news' : 'videoV2',
        ),
      (x) => x.data ?? [],
    )
    return mapRecursive(result, {
      id: (x) => x.value,
      name: (x) => x.label,
      children: (x) => x.children,
    })
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const data = await platformService.DataService.getAccountReport(Platforms.BaiJiaHao, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const videoOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.BaiJiaHao,
          false,
          cookie,
          'video',
        ),
      (x) => x.data || [],
    )
    const dynamicOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.BaiJiaHao,
          false,
          cookie,
          'article',
        ),
      (x) => x.data || [],
    )
    const miniVideoOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.BaiJiaHao,
          false,
          cookie,
          'miniVideo',
        ),
      (x) => x.data || [],
    )
    return [...videoOverviews, ...dynamicOverviews, ...miniVideoOverviews]
  }
}

export const baiJiaHaoPlatformService = new BaiJiaHaoPlatformService()
