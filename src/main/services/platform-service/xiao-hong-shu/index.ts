import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type {
  XiaohongshuDynamicTaskInput,
  XiaohongshuVideoTaskInput,
} from '@yixiaoer/platform-service/dist/media-platform/xiaohongshu'
import type { PlatformDataItem } from '@common/structure'
import { type DataColumn, Platforms } from '@yixiaoer/platform-service'

class XiaoHongShuPlatformService extends PlatformService {
  constructor() {
    super(platformNames.XiaoHongShu)
  }
  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Xiaohongshu.publishVideo(
          cookie,
          body as XiaohongshuVideoTaskInput,
          eventEmitter,
        ),
      progressCallBack,
    )

    return result.publishId!
  }

  // 推送图文
  async pushDynamic(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Xiaohongshu.publishDynamic(
          cookie,
          body as XiaohongshuDynamicTaskInput,
          eventEmitter,
        ),
      progressCallBack,
    )
    return result.publishId!
  }

  // 推送图文混排
  async pushArticle(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Xiaohongshu.publishArticle(
          cookie,
          body as XiaohongshuDynamicTaskInput,
          eventEmitter,
        ),
      progressCallBack,
    )
    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Xiaohongshu.getXiaohongshuUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.XiaoHongShu,
      info.userId.toString(),
      info.userName,
      info.userAvatar,
    )
  }

  async getTopics(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Xiaohongshu.getXiaohongshuTopicList(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.data?.topic_info_dtos ?? [],
    )

    return result.map((item) => ({
      id: item.id,
      text: item.name,
      raw: item,
    }))
  }

  async getFriends(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Xiaohongshu.getXiaohongshuFriendList(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.data?.user_info_dtos ?? [],
    )

    return result.map((item) => ({
      id: item.user_base_dto.user_id,
      text: item.user_base_dto.user_nickname,
      raw: item,
    }))
  }

  async getLocations(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Xiaohongshu.getXiaohongshuLocationList(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.data ?? [],
    )

    return result.map((item) => ({
      id: item.poiLongId.toString(),
      text: item.name,
      raw: item,
    }))
  }

  async queryAccountOverview(
    cookies: Electron.Cookie[],
  ): Promise<{ video?: DataColumn[]; dynamic?: DataColumn[]; article?: DataColumn[] }> {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const data = await platformService.DataService.getAccountReport(Platforms.XiaoHongShu, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.XiaoHongShu,
          false,
          cookie,
        ),
      (x) => x.data || [],
    )
  }
}

export const xiaoHongShuPlatformService = new XiaoHongShuPlatformService()
