import type { QueryStateScheduledTask } from './schedule/query-state/query-state'
import { QueryStateTaskScheduler } from './schedule/query-state/query-state'

class StateQueryScheduleService {
  private scheduler = new QueryStateTaskScheduler(3)

  public push(task: QueryStateScheduledTask) {
    return this.scheduler.push(task)
  }
}

export const stateQuerySchedulerService = new StateQueryScheduleService()
