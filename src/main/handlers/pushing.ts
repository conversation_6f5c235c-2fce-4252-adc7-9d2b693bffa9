import { ipcMain } from 'electron'
import { uiEvents } from '@common/events/ui-events'
import { getPlatformService } from '@main/services/platform-service/factory'
import {
  pushingSchedulerService,
  stateQuerySchedulerService,
} from '@main/services/schedule-service'
import { mockAuditStateQuery } from '@main/services/mockConfig'
import { QueryStateScheduledTask } from '@main/services/schedule-service/schedule/query-state/query-state'
import { PushingScheduledTask } from '@main/services/schedule-service/schedule/pushing/pushing'
import type { EditContentType } from '@common/model/content-type'
import { PushContentType } from '@common/model/content-type'
import { publishLogService } from '@main/services/publish-log-service'
import { eventBus } from '@main/services/eventBus/eventBus'
import { publishEvents } from '@main/services/eventBus/event/events'
import { publishChannels } from '@common/events/publish-chennels'
import type { AccountSession } from '@common/structure'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '@main/services/platform-service/import-promise'
import { systemService } from '@main/services/system-service'
import type { AccountInfoStructure } from '@common/model/account-info'
import type { CloudPublishTaskVideoFromLite } from '@yixiaoer/platform-service'

const pushFuns = {
  [PushContentType.VerticalVideo]: (platformName: string) =>
    getPlatformService(platformName).pushVideo,
  [PushContentType.HorizonVideo]: (platformName: string) =>
    getPlatformService(platformName).pushVideo,
  [PushContentType.Video]: (platformName: string) => getPlatformService(platformName).pushVideo,
  [PushContentType.ImageText]: (platformName: string) =>
    getPlatformService(platformName).pushDynamic,
  [PushContentType.Article]: (platformName: string) => getPlatformService(platformName).pushArticle,
}

export default {
  registerHandlers(_browserWindow: Electron.BrowserWindow) {
    ipcMain.handle(
      uiEvents.pushVideo,
      (
        _event,
        taskId: string,
        platformName: string,
        contentTypeName: string,
        accountId: string,
        authorId: string,
        wechatLockToken: [string, string] | null,
        cookies: Electron.Cookie[],
        body: unknown,
      ): void => {
        console.log('收到wechatLockToken', wechatLockToken)
        let interval: NodeJS.Timeout | null = null
        if (wechatLockToken) {
          _browserWindow.webContents.send(
            publishChannels.wechatKeepAlive,
            wechatLockToken[0],
            wechatLockToken[1],
          )
          interval = setInterval(() => {
            _browserWindow.webContents.send(
              publishChannels.wechatKeepAlive,
              wechatLockToken[0],
              wechatLockToken[1],
            )
          }, 10000) // 10秒发送一次
        }

        pushingSchedulerService.push(
          new PushingScheduledTask(
            taskId,
            //TODO 第三方授权微信视频号应该按照父级账号互斥
            platformName === platformNames.WeiXinShiPinHao && cookies.length === 0 // 第三方授权微信视频号按照平台互斥
              ? platformName
              : `${platformName}:${authorId}`,
            async () => {
              try {
                const pushFun = pushFuns[contentTypeName](platformName)
                const publishId = await pushFun.call(
                  getPlatformService(platformName),
                  contentTypeName,
                  accountId,
                  authorId,
                  cookies,
                  body,
                  (progress: number, message: string) => {
                    _browserWindow.webContents.send(
                      uiEvents.pushVideoProgress,
                      taskId,
                      progress,
                      message,
                    )
                  },
                  wechatLockToken,
                )
                _browserWindow.webContents.send(
                  uiEvents.pushVideoSuccess,
                  platformName,
                  accountId,
                  taskId,
                  publishId,
                  wechatLockToken,
                )
                publishLogService.logPublishSuccess(taskId, publishId)
              } catch (e) {
                _browserWindow.webContents.send(
                  uiEvents.pushVideoFailed,
                  platformName,
                  accountId,
                  taskId,
                  e,
                  wechatLockToken,
                )
                publishLogService.logPublishFail(taskId, e)
              } finally {
                interval && clearInterval(interval)
              }
            },
          ),
        )
      },
    )
    ipcMain.handle(
      uiEvents.auditStateQuery,
      (
        _event,
        taskId: string,
        platformName: string,
        contentType: EditContentType,
        account: AccountInfoStructure,
        publishId: string,
      ) => {
        return getPlatformService(platformName).queryState(taskId, contentType, account, publishId)
      },
    )

    ipcMain.handle(
      uiEvents.scheduleStateQuery,
      (_event, taskSetId: string, scheduleDatetime: Date) => {
        // mock模式下修正预约时间为10秒后
        if (process.env.NODE_ENV === 'development' && mockAuditStateQuery) {
          const now = new Date(new Date().getTime() + 10000)
          scheduleDatetime = now < scheduleDatetime ? now : scheduleDatetime
        }

        stateQuerySchedulerService.push(
          new QueryStateScheduledTask(scheduleDatetime, taskSetId, 'all', async () => {
            _browserWindow.webContents.send(uiEvents.stateQueryStart, taskSetId)
          }),
        )
      },
    )

    eventBus.on(
      publishEvents.getAccountSession,
      (requestId: string, accountId: string, wechatToken: string) => {
        console.debug('getAccountSession111', requestId, accountId, wechatToken)
        _browserWindow.webContents.send(
          publishChannels.getAccountSession,
          requestId,
          accountId,
          wechatToken,
        )
      },
    )

    ipcMain.on(
      publishChannels.getAccountSessionReply,
      async (event, requestId: string, session: AccountSession) => {
        console.debug('getAccountSessionReply', requestId, session)
        eventBus.emit(publishEvents.getAccountSessionReply, requestId, session)
      },
    )

    ipcMain.on(publishChannels.getAccountSessionFailed, async (event, requestId: string) => {
      console.debug('getAccountSessionFailed', requestId)
      eventBus.emit(publishEvents.getAccountSessionFailed, requestId)
    })

    ipcMain.handle(uiEvents.pushAppTask, async (_event, body: CloudPublishTaskVideoFromLite) => {
      console.debug('pushAppTask', systemService.getAuthorizationToken(), body)
      const service = await getPlatformServicePromise()
      const result = await service.Cloud.pushPublishLiteClientTask(body)
      console.debug('任务接收结果', result)
    })

    ipcMain.handle(uiEvents.getUnfinishedTaskIds, async () => {
      const service = await getPlatformServicePromise()
      return service.Cloud.getPublishingTasks()
    })

    ipcMain.handle(uiEvents.cancelPublishTask, async (_event, taskId: string) => {
      console.debug('cancelPublishTask', taskId)
      try {
        const service = await getPlatformServicePromise()
        return service.Cloud.cancelPublishTask(taskId)
      } catch (error) {
        console.error('cancelPublishTask failed:', error)
        throw error
      }
    })
  },
}
